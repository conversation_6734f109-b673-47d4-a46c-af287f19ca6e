<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use App\Models\Routes;

// Route::get('/', function () {
//     return view('dashboard');
// });

Route::get('/', 'LoginController@index');
Route::get('/login', 'LoginController@index')->name('login');
Route::POST('/login/store', 'LoginController@store')->name('login.store');
Route::GET('/logout','LoginController@destroy');
// Route::GET('/send-mail','CauseStopController@sendMail');
// Route::get('/my-upload', 'MyUploadController@index'); // Commented out - using dynamic routes with permissions

// Route::get('/dashboard', 'DashboardController@index')->middleware('authz');
try {
    Route::GET('/api/tesmail/send/{to}', 'Tesmail@send');
    Route::GET('/api/tesmail/', 'Tesmail@index');
    Route::POST('/api/tesmail/eq', 'Tesmail@EQ');
    Route::GET('/api/tesmail/get_temp', 'Tesmail@get_temp');
    Route::GET('/api/tesmail/cenv', 'Tesmail@Cenv');
    $routes = Routes::where('guard', 'web')->get()->toArray();

    Route::middleware(['prevent-back-history', 'sql', 'setlang'])->group(function () use ($routes) {
        foreach ($routes as $key) {
            # code...

            $arr = explode(',', $key['middleware']);
            $method = $key['method'];
            $url = $key['url'];
            $path = $key['route'];
            if ($key['permission'] != '') {
                # code...
                $perm = "permissionAccess:";
                if ($key['type'] != 'data') {
                    $perm = 'permission:';
                }
                array_push($arr, $perm . $key['permission']);
            }
            Route::$method($url, $path)->middleware($arr);
        }
    });
} catch (Exception $e) {
}


