@extends('layouts.main')

@section('title', 'My Upload')

@section('css_page')
<!-- BEGIN VENDOR CSS-->
<!-- END VENDOR CSS-->

<!-- BEGIN Page Level CSS-->
<style>
  /* width */
  ::-webkit-scrollbar {
    width: 5px;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    background: #c8c8c8;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  /* color danger */
  .color-danger {
    color: #ff0000;
  }

  .text-danger {
    color: #F64E60 !important;
  }
</style>
<!-- END Page Level CSS-->
@endsection

@section('content')
<div class="content classMasterData d-flex flex-column flex-column-fluid" style="padding-top: 10px !important; padding-bottom: 0px !important;" id="kt_content">
  <!--begin::Subheader-->
  {{-- <div class="subheader py-2 py-lg-4 subheader-solid" id="kt_subheader">
    <div class="container-fluid d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
      <!--begin::Info-->
      <div class="d-flex align-items-center flex-wrap mr-2">
        <!--begin::Page Title-->
        <span class="text-muted font-weight-bold mr-4">
          <i class="fa fa-layer-group text-primary"></i>
        </span>
        <!--end::Page Title-->
        <!--begin::Actions-->
        <div class="subheader-separator subheader-separator-ver mt-2 mb-2 mr-4 bg-gray-200"></div>
        <h5 class="text-dark font-weight-bold mt-2 mb-2 mr-5">Cost Center</h5>
        <!--end::Actions-->
      </div>
      <!--end::Info-->
      <!--begin::Toolbar-->
      <!--end::Toolbar-->
    </div>
  </div> --}}
  <!--end::Subheader-->
  <!--begin::Entry-->
  <div class="d-flex flex-column-fluid">
    <!--begin::Container-->
    <div class="container-fluid">
      <!--begin::Notice-->
      <!--end::Notice-->
      <!--begin::Card-->
      <div class="card card-custom margin-bot-card">
        <div class="card-header flex-wrap py-3">
          <div class="card-title">
            <h3 class="card-label">Data Upload</h3>
            <span class="d-block text-muted pt-2 font-size-sm"></span>
            </h3>
          </div>
          <div class="card-toolbar">
            @can('my-upload-C')
            <!--begin::Button-->
            <button id="addMenu" name="addMenu" class="btn btn-primary font-weight-bolder">
              <span class="svg-icon svg-icon-md">
                <!--begin::Svg Icon | path:assets/media/svg/icons/Design/Flatten.svg-->
                <!--end::Svg Icon-->
              </span>Upload Dokumen
            </button>
            @endcan
            <!--end::Button-->
          </div>
        </div>
        <div class="card-body">
          <!--begin: Search Form-->
          <!--begin::Search Form-->
          <div class="mb-7">
            <div class="row align-items-center">
              <div class="col-lg-9 col-xl-8">
                <div class="row align-items-center">
                  <div class="col-md-4 my-2 my-md-0">
                    <div class="input-icon">
                      <input type="text" class="form-control" placeholder="Search..." id="kt_datatable_search_query" />
                      <span>
                        <i class="flaticon2-search-1 text-muted"></i>
                      </span>
                    </div>
                  </div>
                  <div class="col-md-2">
                    <button type="button" class="btn btn-danger font-weight-bold px-6" id="btn-search">Search</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!--end::Search Form-->
          <!--end: Search Form-->
          <!--begin: Datatable-->
          <div class="datatable datatable-bordered datatable-head-custom " id="kt_datatable_menu"></div>
          <!--end: Datatable-->
        </div>
      </div>
      <!--end::Card-->
    </div>
    <!--end::Container-->
  </div>
  <!--end::Entry-->
</div>

<!--begin:Modal-->
<div class="modal fade" id="modalMenu" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
  aria-hidden="true">
  <div class="modal-dialog modal-dialog-scrollable" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="modalMenuTitle">Upload</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <i aria-hidden="true" class="ki ki-close"></i>
        </button>
      </div>
      <!--begin:Form-->
      <div class="modal-body" style="height: 400px;overflow-x: hidden;">
        <form role="form" class="form" name="formmenus" id="formmenus" enctype="multipart/formdata" method="">
          <div class="mb-7">
            <div class="form-group row">
              <label class="col-lg-3 col-form-label">Title<span class="color-danger">*</span>:</label>
              <div class="col-lg-9">
                <input type="text" class="form-control" id="title" name="title"/>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-lg-3 col-form-label">Category<span class="color-danger">*</span>:</label>
              <div class="col-lg-9">
                <select class="form-control select2" name="category_id" id="category_id" style="width: 100%;">
                  @foreach ($categories as $detail)
                  <option value="{{ $detail['id'] }}">{!! $detail['name'] !!}</option>
                  @endforeach
                </select>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-lg-3 col-form-label">Description<span class="color-danger"></span>:</label>
              <div class="col-lg-9">
                  <textarea class="form-control" id="description" name="description" ></textarea>
              </div>
            </div>
            <div class="form-group row">
              <label class="col-lg-3 col-form-label">File<span class="color-danger">*</span>:</label>
              <div class="col-lg-9">
                  <input type="file" accept="application/pdf,image/*,video/mp4" class="form-control" id="file" name="file" >
              </div>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-light-danger font-weight-bold" data-dismiss="modal"><i
            class="fa fa-times"></i>Cancel
        </button>
        @can(['my-upload-C' , 'my-upload-U'])
        <button type="submit" id="saveMenu" data-id="" class="btn btn-success font-weight-bold">
          <i class="fa fa-save"></i> Save changes
        </button>
        @endcan
      </div>
      <!--end:Form-->
    </div>
  </div>
</div>

@endsection

@section('js_page')
<!--begin::Page Vendors(used by this page)-->
<!--end::Page Vendors-->
<!--begin::Page Scripts(used by this page)-->
<!--end::Page Scripts-->

<script type="text/javascript">
  $(document).ready(function () {
    if (document.getElementsByClassName("classMasterData")) {
        var element = document.getElementById("kt_wrapper");
        element.classList.add("headerSync");
    }
    $('.select2').select2();

    var datatable = $('#kt_datatable_menu');

    @can('my-upload-R')

    datatable.KTDatatable({
        // datasource definition
        data: {
            type: 'remote',
            source: {
                read: {
                    url: '/my-upload/list',
                    method: 'GET',
                }
            },
            pageSize: 10,
        },
        // layout definition
        layout: {
            scroll: false, // enable/disable datatable scroll both horizontal and vertical when needed.
            footer: false // display/hide footer
        },
        // column sorting
        sortable: true,
        pagination: true,
        rows: {
                autoHide: false,
            },
        search: {
            input: $('#kt_datatable_search_query'),
            key: 'generalSearch',
            onEnter: true,
        },
        // columns definition
        columns: [
            {
                field: 'title',
                title: 'Title',
                width: 100,
            }, {
                field: 'category_name',
                title: 'Category',
                width: 100,
            }, {
                field: 'description',
                title: 'Description',
                width: 100,
            }, {
                field: 'status',
                title: 'Status',
                width: 100,
            }, {
                field: 'created_at',
                title: 'Tahun',
                width: 100,
            },
            {
                field: 'Actions',
                title: 'Actions',
                sortable: false,
                width: 240,
                autoHide: false,
                overflow: 'visible',
                template: function (row) {
                    let btnApprove = '';
                    if(row.status == 'Waiting'){
                        btnApprove = `<button type='button' class='approve btn btn-sm btn-icon btn-outline-warning ' title='Approve' data-toggle='tooltip' data-action='approve' data-id="${row.id}" ><i class='fa fa-check'></i> </button>
                        <button type='button' class='reject btn btn-sm btn-icon btn-outline-warning ' title='Reject' data-toggle='tooltip' data-action='reject' data-id="${row.id}" ><i class='fa fa-times'></i> </button>`;
                    }
                    return "<center>" +
                            @can('my-upload-U')
                                "<button type='button' class='edits btn btn-sm btn-icon btn-outline-warning ' title='Edit' data-toggle='tooltip' data-id=" + row.id + " ><i class='fa fa-edit'></i> </button>  " +
                            @endcan
                            @can('my-upload-A')
                                btnApprove +
                            @endcan
                            "</center>";
                },
            }
        ],

    });

    @endcan

    $(document).on('click', '#saveMenu', function() {
        $('#formmenus').trigger('submit');
    })

    $(document).on('click', '#btn-search', function() {
        datatable.search($("#kt_datatable_search_query").val());
    })

    @can('my-upload-C')
    $(document).on('click', '#addMenu', function () {
        $("#saveMenu").data("id", "");
        $('#modalMenuTitle').text('Upload Dokumen');
        $('#modalMenu').modal('show');
        $(`.form-control`).removeClass('is-invalid');
        $(`.invalid-feedback`).remove();
        let form = document.forms.formmenus; // <form name="formmenus"> element
        form.reset();
    });
    @endcan

    @can('my-upload-A')
    $(document).on('click', '.approve,.reject', function () {
        let id = $(this).data('id');
        let action = $(this).data('action');
        let url = `./my-upload/${id}/${action}`;
        Swal.fire({
            title: "Konfirmasi",
            text: `Yakin akan melakukan ${action} ke dokumen ini`,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes"
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    type: 'POST', 
                    url: url, 
                })
                .done(function (data) {
                    showtoastr('success', data.message);
                })
                .fail(function (data) {
                    show_toastr('error', data.responseJSON.status, data.responseJSON.message);
                    $.each(data.responseJSON.messages, function (index, value) {
                        show_toastr('error', index, value);
                    });
                })
                .always(function () {
                    datatable.reload();
                });
              }
            });
    });
    @endcan

    @can('my-upload-U')
    $(document).on('click', '.edits', function () {
        $.ajax({
            type: 'GET', // define the type of HTTP verb we want to use (POST for our form)
            url: './my-upload/' + $(this).data('id'), // the url where we want to POST
            beforeSend: function () {
                let form = document.forms.formmenus; // <form name="formmenus"> element
                form.reset();
                $(`.form-control`).removeClass('is-invalid');
                $(`.invalid-feedback`).remove();
            }
        }).done(function (res) {
            let form = document.forms.formmenus; // <form name="formmenus"> element
            if (res.success) {
                showtoastr('success', res.message);
                $(form.elements.title).val(res.data.title);
                $(form.elements.description).val(res.data.description);
                $(form.elements.category_id).val(res.data.category_id);
                $("#saveMenu").data( "id", res.data.id);
            }
        }).fail(function (data) {
            show_toastr('error', data.responseJSON.status, data.responseJSON.message);
            $.each(data.responseJSON.errors, function (index, value) {
                show_toastr('error', index, value);
            });
        }).always(function () {
            $('#modalMenuTitle').text('Edit Dokumen');
            $('#modalMenu').modal('show');
        });
    });
    @endcan

    @can(['my-upload-C', 'my-upload-U'])
    $('#formmenus').submit(function (e) {
        e.preventDefault();
        var formData = new FormData($("#formmenus")[0]);
        // var formData = $('#formmenus').serializeArray(); // our data object
        var method = "POST";
        let menuID = $("#saveMenu").data("id");

        if (typeof menuID == "undefined" || menuID == "") {
            var url = `./my-upload`;
        } else {
            var url = `./my-upload/${menuID}/update`;
        }

        $.ajax({
            type: method, // define the type of HTTP verb we want to use (POST for our form)
            url: url, // the url where we want to POST
            data: formData,
            dataType: 'JSON', // what type of data do we expect back from the server
            contentType: false,
            processData: false,
            beforeSend: function () {
                $(`.form-control`).removeClass('is-invalid');
                $(`.invalid-feedback`).remove();
                $('#saveMenu').attr('disabled', true).html("<i class='fa fa-spinner fa-spin'></i> processing");
            }
        }).done(function (data) {
            $("#modalMenu").modal('hide');
            showtoastr('success', data.message);
            $("#saveMenu").data("id", "");
            $("#formmenus")[0].reset();
            menuID = "";
            let form = document.forms.formmenus; // <form name="formmenus"> element
            form.reset();
            datatable.reload();
        }).fail(function (data) {
            show_toastr('error', data.responseJSON.status, data.responseJSON.message);
            $.each(data.responseJSON.errors, function (index, value) {
                if ($(`input[name='${index}']`)) {
                    $(`input[name='${index}']`).addClass(`is-invalid`);
                    $(`input[name='${index}']`).after(`<div class="invalid-feedback">${value}</div>`);
                }
                show_toastr('error', index, value);
            });
        }).always(function () {
            $('#saveMenu').attr('disabled', false).html("<i class='fa fa-save'></i> Save");
        });
    });
    @endcan

    @can('my-upload-D')
    $(document).on('click', '.deletes', function () {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        })
        .then(isConfirm => {
        if(isConfirm.isConfirmed) {
          $.ajax({
              type: 'DELETE', // define the type of HTTP verb we want to use (POST for our form)
              url: './my-upload/' + $(this).data('id'), // the url where we want to POST
          })
          .done(function (data) {
              showtoastr('success', data.message);
          })
          .fail(function (data) {
              show_toastr('error', data.responseJSON.status, data.responseJSON.message);
              $.each(data.responseJSON.messages, function (index, value) {
                  show_toastr('error', index, value);
              });
          })
          .always(function () {
              datatable.reload();
          });
        }
      });
    });
    @endcan
  });


</script>

@endsection